{{- if .Values.migrationJob.enabled }}
# This job runs the prisma migrations for the LiteLLM DB.
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "litellm.fullname" . }}-migrations
  annotations:
    argocd.argoproj.io/hook: PreSync
    argocd.argoproj.io/hook-delete-policy: BeforeHookCreation # delete old migration on a new deploy in case the migration needs to make updates
    checksum/config: {{ toYaml .Values | sha256sum }}
spec:
  template:
    metadata:
      annotations:
        {{- with .Values.migrationJob.annotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      serviceAccountName: {{ include "litellm.serviceAccountName" . }}
      containers:
        - name: prisma-migrations
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default (printf "main-%s" .Chart.AppVersion) }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          command: ["python", "litellm/proxy/prisma_migration.py"]
          workingDir: "/app"
          env:
            {{- if .Values.db.useExisting }}
            - name: DATABASE_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.db.secret.name }}
                  key: {{ .Values.db.secret.usernameKey }}
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.db.secret.name }}
                  key: {{ .Values.db.secret.passwordKey }}
            - name: DATABASE_HOST
              value: {{ .Values.db.endpoint }}
            - name: DATABASE_NAME
              value: {{ .Values.db.database }}
            - name: DATABASE_URL
              value: {{ .Values.db.url | quote }}
            {{- else }}
            - name: DATABASE_URL
              value: postgresql://{{ .Values.postgresql.auth.username }}:{{ .Values.postgresql.auth.password }}@{{ .Release.Name }}-postgresql/{{ .Values.postgresql.auth.database }}
            {{- end }}
            - name: DISABLE_SCHEMA_UPDATE
              value: "false" # always run the migration from the Helm PreSync hook, override the value set
          {{- with .Values.volumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.migrationJob.extraContainers }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.volumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      restartPolicy: OnFailure
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  ttlSecondsAfterFinished: {{ .Values.migrationJob.ttlSecondsAfterFinished }}
  backoffLimit: {{ .Values.migrationJob.backoffLimit }}
{{- end }}
