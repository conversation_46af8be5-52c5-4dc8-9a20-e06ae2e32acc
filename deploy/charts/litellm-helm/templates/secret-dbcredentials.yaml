{{- if .Values.db.deployStandalone -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "litellm.fullname" . }}-dbcredentials
data:
  # Password for the "postgres" user
  postgres-password: {{ ( index .Values.postgresql.auth "postgres-password") | default "litellm" | b64enc }}
  username: {{ .Values.postgresql.auth.username | default "litellm" | b64enc }}
  password: {{ .Values.postgresql.auth.password | default "litellm" | b64enc }}
type: Opaque
{{- end -}}