apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
  {{- toYaml .Values.deploymentAnnotations | nindent 4 }}
  name: {{ include "litellm.fullname" . }}
  labels:
    {{- include "litellm.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "litellm.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap-litellm.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "litellm.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "litellm.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ include "litellm.name" . }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default (printf "main-%s" .Chart.AppVersion) }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: HOST
              value: "{{ .Values.listen | default "0.0.0.0" }}"
            - name: PORT
              value: {{ .Values.service.port | quote}}
            {{- if .Values.db.deployStandalone }}
            - name: DATABASE_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ include "litellm.fullname" . }}-dbcredentials
                  key: username
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "litellm.fullname" . }}-dbcredentials
                  key: password
            - name: DATABASE_HOST
              value: {{ .Release.Name }}-postgresql
            - name: DATABASE_NAME
              value: litellm
            {{- else if .Values.db.useExisting }}
            - name: DATABASE_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.db.secret.name }}
                  key: {{ .Values.db.secret.usernameKey }}
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.db.secret.name }}
                  key: {{ .Values.db.secret.passwordKey }}
            - name: DATABASE_HOST
              value: {{ .Values.db.endpoint }}
            - name: DATABASE_NAME
              value: {{ .Values.db.database }}
            - name: DATABASE_URL
              value: {{ .Values.db.url | quote }}
            {{- end }}
            - name: PROXY_MASTER_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.masterkeySecretName | default (printf "%s-masterkey" (include "litellm.fullname" .)) }}
                  key: {{ .Values.masterkeySecretKey | default "masterkey" }}
            {{- if .Values.redis.enabled }}
            - name: REDIS_HOST
              value: {{ include "litellm.redis.serviceName" . }}
            - name: REDIS_PORT
              value: {{ include "litellm.redis.port" . | quote }}
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "redis.secretName" .Subcharts.redis }}
                  key: {{include "redis.secretPasswordKey" .Subcharts.redis }}
            {{- end }}
            {{- if .Values.envVars }}
            {{- range $key, $val := .Values.envVars }}
            - name: {{ $key }}
              value: {{ $val | quote }}
            {{- end }}
            {{- end }}
            {{- with .Values.extraEnvVars }}
              {{- toYaml . | nindent 12 }}
            {{- end }}
          envFrom:
          {{- range .Values.environmentSecrets }}
            - secretRef:
                name: {{ . }}
          {{- end }}
          {{- range .Values.environmentConfigMaps }}
            - configMapRef:
                name: {{ . }}
          {{- end }}
          args:
            - --config
            - /etc/litellm/config.yaml
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /health/liveliness
              port: http
          readinessProbe:
            httpGet:
              path: /health/readiness
              port: http
          # Give the container time to start up.  Up to 5 minutes (10 * 30 seconds)
          startupProbe:
            httpGet:
              path: /health/readiness
              port: http
            failureThreshold: 30
            periodSeconds: 10
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: litellm-config
              mountPath: /etc/litellm/
          {{ if .Values.securityContext.readOnlyRootFilesystem }}
            - name: tmp
              mountPath: /tmp
            - name: cache
              mountPath: /.cache
            - name: npm
              mountPath: /.npm
          {{- end }}
          {{- with .Values.volumeMounts }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.extraContainers }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
        {{ if .Values.securityContext.readOnlyRootFilesystem }}
        - name: tmp
          emptyDir:
            sizeLimit: 500Mi
        - name: cache
          emptyDir:
            sizeLimit: 500Mi
        - name: npm
          emptyDir:
            sizeLimit: 500Mi
        {{- end }}
        - name: litellm-config
          configMap:
            name: {{ include "litellm.fullname" . }}-config
            items:
              - key: "config.yaml"
                path: "config.yaml"
      {{- with .Values.volumes }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
