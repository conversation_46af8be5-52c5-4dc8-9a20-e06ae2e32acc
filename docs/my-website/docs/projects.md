# Projects Built on LiteLLM



### EntoA<PERSON>
Chat and Ask on your own data.
[Github](https://github.com/akshata29/entaoai)

### GPT-Migrate
Easily migrate your codebase from one framework or language to another.
[Github](https://github.com/0xpayne/gpt-migrate)

### Otter
Otter, a multi-modal model based on OpenFlamingo (open-sourced version of DeepMind's Flamingo), trained on MIMIC-IT and showcasing improved instruction-following and in-context learning ability.
[Github](https://github.com/Luodian/Otter)




