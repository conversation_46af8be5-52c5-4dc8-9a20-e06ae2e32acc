[tool.poetry]
name = "litellm-enterprise"
version = "0.1.11"
description = "Package for LiteLLM Enterprise features"
authors = ["BerriAI"]
readme = "README.md"


[tool.poetry.urls]
homepage = "https://litellm.ai"
Homepage = "https://litellm.ai"
repository = "https://github.com/BerriAI/litellm"
Repository = "https://github.com/BerriAI/litellm"
documentation = "https://docs.litellm.ai"
Documentation = "https://docs.litellm.ai"

[tool.poetry.dependencies]
python = ">=3.8.1,<4.0, !=3.9.7"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.commitizen]
version = "0.1.11"
version_files = [
    "pyproject.toml:version",
    "../requirements.txt:litellm-enterprise==",
    "../pyproject.toml:litellm-enterprise = {version = \""
]