name: Reset litellm_stable branch

on:
  release:
    types: [published, created]
jobs:
  update-stable-branch:
    if: ${{ startsWith(github.event.release.tag_name, 'v') && !endsWith(github.event.release.tag_name, '-stable') }}
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Reset litellm_stable_release_branch branch to the release commit
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          # Configure Git user
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          
          # Fetch all branches and tags
          git fetch --all

          # Check if the litellm_stable_release_branch branch exists
          if git show-ref --verify --quiet refs/remotes/origin/litellm_stable_release_branch; then
            echo "litellm_stable_release_branch branch exists."
            git checkout litellm_stable_release_branch
          else
            echo "litellm_stable_release_branch branch does not exist. Creating it."
            git checkout -b litellm_stable_release_branch
          fi

          # Reset litellm_stable_release_branch branch to the release commit
          git reset --hard $GITHUB_SHA

          # Push the updated litellm_stable_release_branch branch
          git push origin litellm_stable_release_branch --force
