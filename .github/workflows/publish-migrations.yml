name: Publish Prisma Migrations

permissions:
  contents: write
  pull-requests: write

on:
  push:
    paths:
      - 'schema.prisma'  # Check root schema.prisma
    branches:
      - main

jobs:
  publish-migrations:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_DB: temp_db
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      # Add shadow database service
      postgres_shadow:
        image: postgres:14
        env:
          POSTGRES_DB: shadow_db
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        ports:
          - 5433:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'

      - name: Install Dependencies
        run: |
          pip install prisma
          pip install python-dotenv

      - name: Generate Initial Migration if None Exists
        env:
          DATABASE_URL: "postgresql://postgres:postgres@localhost:5432/temp_db"
          DIRECT_URL: "postgresql://postgres:postgres@localhost:5432/temp_db"
          SHADOW_DATABASE_URL: "postgresql://postgres:postgres@localhost:5433/shadow_db"
        run: |
          mkdir -p deploy/migrations
          echo 'provider = "postgresql"' > deploy/migrations/migration_lock.toml
          
          if [ -z "$(ls -A deploy/migrations/2* 2>/dev/null)" ]; then
            echo "No existing migrations found, creating baseline..."
            VERSION=$(date +%Y%m%d%H%M%S)
            mkdir -p deploy/migrations/${VERSION}_initial
            
            echo "Generating initial migration..."
            # Save raw output for debugging
            prisma migrate diff \
              --from-empty \
              --to-schema-datamodel schema.prisma \
              --shadow-database-url "${SHADOW_DATABASE_URL}" \
              --script > deploy/migrations/${VERSION}_initial/raw_migration.sql
            
            echo "Raw migration file content:"
            cat deploy/migrations/${VERSION}_initial/raw_migration.sql
            
            echo "Cleaning migration file..."
            # Clean the file
            sed '/^Installing/d' deploy/migrations/${VERSION}_initial/raw_migration.sql > deploy/migrations/${VERSION}_initial/migration.sql
            
            # Verify the migration file
            if [ ! -s deploy/migrations/${VERSION}_initial/migration.sql ]; then
              echo "ERROR: Migration file is empty after cleaning"
              echo "Original content was:"
              cat deploy/migrations/${VERSION}_initial/raw_migration.sql
              exit 1
            fi
            
            echo "Final migration file content:"
            cat deploy/migrations/${VERSION}_initial/migration.sql
            
            # Verify it starts with SQL
            if ! head -n 1 deploy/migrations/${VERSION}_initial/migration.sql | grep -q "^--\|^CREATE\|^ALTER"; then
              echo "ERROR: Migration file does not start with SQL command or comment"
              echo "First line is:"
              head -n 1 deploy/migrations/${VERSION}_initial/migration.sql
              echo "Full content is:"
              cat deploy/migrations/${VERSION}_initial/migration.sql
              exit 1
            fi
            
            echo "Initial migration generated at $(date -u)" > deploy/migrations/${VERSION}_initial/README.md
          fi

      - name: Compare and Generate Migration
        if: success()
        env:
          DATABASE_URL: "postgresql://postgres:postgres@localhost:5432/temp_db"
          DIRECT_URL: "postgresql://postgres:postgres@localhost:5432/temp_db"
          SHADOW_DATABASE_URL: "postgresql://postgres:postgres@localhost:5433/shadow_db"
        run: |
          # Create temporary migration workspace
          mkdir -p temp_migrations
          
          # Copy existing migrations (will not fail if directory is empty)
          cp -r deploy/migrations/* temp_migrations/ 2>/dev/null || true
          
          VERSION=$(date +%Y%m%d%H%M%S)
          
          # Generate diff against existing migrations or empty state
          prisma migrate diff \
            --from-migrations temp_migrations \
            --to-schema-datamodel schema.prisma \
            --shadow-database-url "${SHADOW_DATABASE_URL}" \
            --script > temp_migrations/migration_${VERSION}.sql
          
          # Check if there are actual changes
          if [ -s temp_migrations/migration_${VERSION}.sql ]; then
            echo "Changes detected, creating new migration"
            mkdir -p deploy/migrations/${VERSION}_schema_update
            mv temp_migrations/migration_${VERSION}.sql deploy/migrations/${VERSION}_schema_update/migration.sql
            echo "Migration generated at $(date -u)" > deploy/migrations/${VERSION}_schema_update/README.md
          else
            echo "No schema changes detected"
            exit 0
          fi

      - name: Verify Migration
        if: success()
        env:
          DATABASE_URL: "postgresql://postgres:postgres@localhost:5432/temp_db"
          DIRECT_URL: "postgresql://postgres:postgres@localhost:5432/temp_db"
          SHADOW_DATABASE_URL: "postgresql://postgres:postgres@localhost:5433/shadow_db"
        run: |
          # Create test database
          psql "${SHADOW_DATABASE_URL}" -c 'CREATE DATABASE migration_test;'
          
          # Apply all migrations in order to verify
          for migration in deploy/migrations/*/migration.sql; do
            echo "Applying migration: $migration"
            psql "${SHADOW_DATABASE_URL}" -f $migration
          done

      # Add this step before create-pull-request to debug permissions
      - name: Check Token Permissions
        run: |
          echo "Checking token permissions..."
          curl -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
               -H "Accept: application/vnd.github.v3+json" \
               https://api.github.com/repos/BerriAI/litellm/collaborators
          
          echo "\nChecking if token can create PRs..."
          curl -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
               -H "Accept: application/vnd.github.v3+json" \
               https://api.github.com/repos/BerriAI/litellm

      # Add this debug step before git push
      - name: Debug Changed Files
        run: |
          echo "Files staged for commit:"
          git diff --name-status --staged
          
          echo "\nAll changed files:"
          git status

      - name: Create Pull Request
        if: success()
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "chore: update prisma migrations"
          title: "Update Prisma Migrations"
          body: |
            Auto-generated migration based on schema.prisma changes.
            
            Generated files:
            - deploy/migrations/${VERSION}_schema_update/migration.sql
            - deploy/migrations/${VERSION}_schema_update/README.md
          branch: feat/prisma-migration-${{ env.VERSION }}
          base: main
          delete-branch: true

      - name: Generate and Save Migrations
        run: |
          # Only add migration files
          git add deploy/migrations/
          git status  # Debug what's being committed
          git commit -m "chore: update prisma migrations" 
