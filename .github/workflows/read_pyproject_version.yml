name: Read Version from pyproject.toml

on:
  push:
    branches:
      - main  # Change this to the default branch of your repository

jobs:
  read-version:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.8  # Adjust the Python version as needed

      - name: Install dependencies
        run: pip install toml

      - name: Read version from pyproject.toml
        id: read-version
        run: |
          version=$(python -c 'import toml; print(toml.load("pyproject.toml")["tool"]["commitizen"]["version"])')
          printf "LITELLM_VERSION=%s" "$version" >> $GITHUB_ENV

      - name: Display version
        run: echo "Current version is $LITELLM_VERSION"
