name: Publish Dev Release to PyPI

on:
  workflow_dispatch:
  
jobs:
  publish-dev-release:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.8  # Adjust the Python version as needed

      - name: Install dependencies
        run: pip install toml twine

      - name: Read version from pyproject.toml
        id: read-version
        run: |
          version=$(python -c 'import toml; print(toml.load("pyproject.toml")["tool"]["commitizen"]["version"])')
          printf "LITELLM_VERSION=%s" "$version" >> $GITHUB_ENV

      - name: Check if version exists on PyPI
        id: check-version
        run: |
          set -e
          if twine check --repository-url https://pypi.org/simple/ "litellm==$LITELLM_VERSION" >/dev/null 2>&1; then
            echo "Version $LITELLM_VERSION already exists on PyPI. Skipping publish."
     
