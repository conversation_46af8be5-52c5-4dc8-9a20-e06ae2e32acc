name: Test Locust Load Test

on:
  workflow_run:
    workflows: ["Build, Publish LiteLLM Docker Image. New Release"]
    types:
      - completed
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v1
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.x'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install PyGithub
      - name: re-deploy proxy
        run: |
          echo "Current working directory: $PWD"
          ls
          python ".github/workflows/redeploy_proxy.py"
        env:
          LOAD_TEST_REDEPLOY_URL1: ${{ secrets.LOAD_TEST_REDEPLOY_URL1 }}
          LOAD_TEST_REDEPLOY_URL2: ${{ secrets.LOAD_TEST_REDEPLOY_URL2 }}
        working-directory: ${{ github.workspace }}
      - name: Run Load Test
        id: locust_run
        uses: BerriAI/locust-github-action@master
        with:
          LOCUSTFILE: ".github/workflows/locustfile.py"
          URL:  "https://post-release-load-test-proxy.onrender.com/"
          USERS: "20"
          RATE: "20"
          RUNTIME: "300s"
      - name: Process Load Test Stats
        run: |
          echo "Current working directory: $PWD"
          ls
          python ".github/workflows/interpret_load_test.py"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        working-directory: ${{ github.workspace }}
      - name: Upload CSV as Asset to Latest Release
        uses: xresloader/upload-to-github-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          file: "load_test_stats.csv;load_test.html"
          update_latest_release: true
          tag_name: "load-test"
          overwrite: true