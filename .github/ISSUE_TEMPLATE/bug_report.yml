name: Bug Report
description: File a bug report
title: "[Bug]: "
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!
  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen?
      placeholder: Tell us what you see!
      value: "A bug happened!"
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Relevant log output
      description: Please copy and paste any relevant log output. This will be automatically formatted into code, so no need for backticks.
      render: shell
  - type: dropdown
    id: ml-ops-team
    attributes:
      label: Are you a ML Ops Team?
      description: This helps us prioritize your requests correctly 
      options:
        - "No"
        - "Yes"
    validations:
      required: true
  - type: input
    id: version
    attributes:
      label: What LiteLLM version are you on ? 
      placeholder: v1.53.1
    validations:
      required: true
  - type: input
    id: contact
    attributes:
      label: Twitter / LinkedIn details 
      description: We announce new features on Twitter + LinkedIn. If this issue leads to an announcement, and you'd like a mention, we'll gladly shout you out!
      placeholder: ex. @krrish_dh / https://www.linkedin.com/in/krish-d/
    validations:
      required: false
