{"cells": [{"cell_type": "markdown", "metadata": {"id": "4FbDOmcj2VkM"}, "source": ["## Use LiteLLM with <PERSON><PERSON>\n", "https://docs.litellm.ai/docs/observability/arize_integration\n", "\n", "This method uses the litellm proxy to send the data to <PERSON><PERSON>. The callback is set in the litellm config below, instead of using OpenInference tracing."]}, {"cell_type": "markdown", "metadata": {"id": "21W8Woog26Ns"}, "source": ["## Install Dependencies"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "xrjKLBxhxu2L"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: litellm in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (1.54.1)\n", "Requirement already satisfied: aiohttp in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from litellm) (3.11.10)\n", "Requirement already satisfied: click in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from litellm) (8.1.7)\n", "Requirement already satisfied: httpx<0.28.0,>=0.23.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from litellm) (0.27.2)\n", "Requirement already satisfied: importlib-metadata>=6.8.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from litellm) (8.5.0)\n", "Requirement already satisfied: jinja2<4.0.0,>=3.1.2 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from litellm) (3.1.4)\n", "Requirement already satisfied: jsonschema<5.0.0,>=4.22.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from litellm) (4.23.0)\n", "Requirement already satisfied: openai>=1.55.3 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from litellm) (1.57.1)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.0.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from litellm) (2.10.3)\n", "Requirement already satisfied: python-dotenv>=0.2.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from litellm) (1.0.1)\n", "Requirement already satisfied: requests<3.0.0,>=2.31.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from litellm) (2.32.3)\n", "Requirement already satisfied: tiktoken>=0.7.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from litellm) (0.7.0)\n", "Requirement already satisfied: tokenizers in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from litellm) (0.21.0)\n", "Requirement already satisfied: anyio in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from httpx<0.28.0,>=0.23.0->litellm) (4.7.0)\n", "Requirement already satisfied: certifi in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from httpx<0.28.0,>=0.23.0->litellm) (2024.8.30)\n", "Requirement already satisfied: httpcore==1.* in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from httpx<0.28.0,>=0.23.0->litellm) (1.0.7)\n", "Requirement already satisfied: idna in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from httpx<0.28.0,>=0.23.0->litellm) (3.10)\n", "Requirement already satisfied: sniffio in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from httpx<0.28.0,>=0.23.0->litellm) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from httpcore==1.*->httpx<0.28.0,>=0.23.0->litellm) (0.14.0)\n", "Requirement already satisfied: zipp>=3.20 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from importlib-metadata>=6.8.0->litellm) (3.21.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from jinja2<4.0.0,>=3.1.2->litellm) (3.0.2)\n", "Requirement already satisfied: attrs>=22.2.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from jsonschema<5.0.0,>=4.22.0->litellm) (24.2.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from jsonschema<5.0.0,>=4.22.0->litellm) (2024.10.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from jsonschema<5.0.0,>=4.22.0->litellm) (0.35.1)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from jsonschema<5.0.0,>=4.22.0->litellm) (0.22.3)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from openai>=1.55.3->litellm) (1.9.0)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from openai>=1.55.3->litellm) (0.6.1)\n", "Requirement already satisfied: tqdm>4 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from openai>=1.55.3->litellm) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from openai>=1.55.3->litellm) (4.12.2)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.0.0->litellm) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.1 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.0.0->litellm) (2.27.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from requests<3.0.0,>=2.31.0->litellm) (3.4.0)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from requests<3.0.0,>=2.31.0->litellm) (2.0.7)\n", "Requirement already satisfied: regex>=2022.1.18 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from tiktoken>=0.7.0->litellm) (2024.11.6)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from aiohttp->litellm) (2.4.4)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from aiohttp->litellm) (1.3.1)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from aiohttp->litellm) (1.5.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from aiohttp->litellm) (6.1.0)\n", "Requirement already satisfied: propcache>=0.2.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from aiohttp->litellm) (0.2.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from aiohttp->litellm) (1.18.3)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from tokenizers->litellm) (0.26.5)\n", "Requirement already satisfied: filelock in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm) (3.16.1)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm) (2024.10.0)\n", "Requirement already satisfied: packaging>=20.9 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm) (24.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /Users/<USER>/Documents/arize/.venv/lib/python3.11/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm) (6.0.2)\n"]}], "source": ["!pip install litellm"]}, {"cell_type": "markdown", "metadata": {"id": "jHEu-TjZ29PJ"}, "source": ["## Set Env Variables"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "QWd9rTysxsWO"}, "outputs": [], "source": ["import litellm\n", "import os\n", "from getpass import getpass\n", "\n", "os.environ[\"ARIZE_SPACE_KEY\"] = getpass(\"Enter your Arize space key: \")\n", "os.environ[\"ARIZE_API_KEY\"] = getpass(\"Enter your Arize API key: \")\n", "os.environ['OPENAI_API_KEY']= getpass(\"Enter your OpenAI API key: \")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's run a completion call and see the traces in Arize"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! Nice to meet you, OpenAI. How can I assist you today?\n"]}], "source": ["# set arize as a callback, litellm will send the data to arize\n", "litellm.callbacks = [\"arize\"]\n", " \n", "# openai call\n", "response = litellm.completion(\n", "  model=\"gpt-3.5-turbo\",\n", "  messages=[\n", "    {\"role\": \"user\", \"content\": \"Hi 👋 - i'm openai\"}\n", "  ]\n", ")\n", "print(response.choices[0].message.content)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 0}