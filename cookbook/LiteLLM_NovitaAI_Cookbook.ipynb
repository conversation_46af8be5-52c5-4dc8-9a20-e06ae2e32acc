{"cells": [{"cell_type": "markdown", "metadata": {"id": "iFEmsVJI_2BR"}, "source": ["# LiteLLM NovitaAI Cookbook"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cBlUhCEP_xj4"}, "outputs": [], "source": ["!pip install litellm"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "p-MQqWOT_1a7"}, "outputs": [], "source": ["import os\n", "\n", "os.environ['NOVITA_API_KEY'] = \"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Ze8JqMqWAARO"}, "outputs": [], "source": ["from litellm import completion\n", "response = completion(\n", "            model=\"novita/deepseek/deepseek-r1\",\n", "            messages=[{\"role\": \"user\", \"content\": \"write code for saying hi\"}]\n", ")\n", "response"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-LnhELrnAM_J"}, "outputs": [], "source": ["response = completion(\n", "            model=\"novita/deepseek/deepseek-r1\",\n", "            messages=[{\"role\": \"user\", \"content\": \"write code for saying hi\"}]\n", ")\n", "response"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "dJBOUYdwCEn1"}, "outputs": [], "source": ["response = completion(\n", "            model=\"mistralai/mistral-7b-instruct\",\n", "            messages=[{\"role\": \"user\", \"content\": \"write code for saying hi\"}]\n", ")\n", "response"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}