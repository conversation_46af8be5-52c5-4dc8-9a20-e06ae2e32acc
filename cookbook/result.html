<!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
            </head>
            <body>
                <div id="app"></div>

                <script>var pyinstrumentHTMLRenderer=function(){"use strict";var ct=Object.defineProperty;var dt=(R,C,H)=>C in R?ct(R,C,{enumerable:!0,configurable:!0,writable:!0,value:H}):R[C]=H;var k=(R,C,H)=>(dt(R,typeof C!="symbol"?C+"":C,H),H);const R="";function C(){}function H(t){return t()}function we(){return Object.create(null)}function O(t){t.forEach(H)}function be(t){return typeof t=="function"}function K(t,e){return t!=t?e==e:t!==e||t&&typeof t=="object"||typeof t=="function"}function Ie(t){return Object.keys(t).length===0}function Re(t,...e){if(t==null)return C;const i=t.subscribe(...e);return i.unsubscribe?()=>i.unsubscribe():i}function se(t,e,i){t.$$.on_destroy.push(Re(e,i))}function f(t,e){t.appendChild(e)}function X(t,e,i){t.insertBefore(e,i||null)}function N(t){t.parentNode&&t.parentNode.removeChild(t)}function _(t){return document.createElement(t)}function ke(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function L(t){return document.createTextNode(t)}function w(){return L(" ")}function ye(){return L("")}function W(t,e,i,n){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i,n)}function Ce(t){return function(e){return e.preventDefault(),t.call(this,e)}}function Ee(t){return function(e){return e.stopPropagation(),t.call(this,e)}}function m(t,e,i){i==null?t.removeAttribute(e):t.getAttribute(e)!==i&&t.setAttribute(e,i)}function Oe(t){return Array.from(t.childNodes)}function q(t,e){e=""+e,t.wholeText!==e&&(t.data=e)}function T(t,e,i,n){i===null?t.style.removeProperty(e):t.style.setProperty(e,i,n?"important":"")}function Q(t,e,i){t.classList[i?"add":"remove"](e)}let V;function Z(t){V=t}function Te(){if(!V)throw new Error("Function called outside component initialization");return V}function qe(t){Te().$$.on_mount.push(t)}function Ue(t){Te().$$.on_destroy.push(t)}const Y=[],Fe=[],j=[],Se=[],Ve=Promise.resolve();let ae=!1;function Ze(){ae||(ae=!0,Ve.then($e))}function ue(t){j.push(t)}const fe=new Set;let x=0;function $e(){const t=V;do{for(;x<Y.length;){const e=Y[x];x++,Z(e),Ye(e.$$)}for(Z(null),Y.length=0,x=0;Fe.length;)Fe.pop()();for(let e=0;e<j.length;e+=1){const i=j[e];fe.has(i)||(fe.add(i),i())}j.length=0}while(Y.length);for(;Se.length;)Se.pop()();ae=!1,fe.clear(),Z(t)}function Ye(t){if(t.fragment!==null){t.update(),O(t.before_update);const e=t.dirty;t.dirty=[-1],t.fragment&&t.fragment.p(t.ctx,e),t.after_update.forEach(ue)}}const ee=new Set;let G;function ce(){G={r:0,c:[],p:G}}function de(){G.r||O(G.c),G=G.p}function D(t,e){t&&t.i&&(ee.delete(t),t.i(e))}function z(t,e,i,n){if(t&&t.o){if(ee.has(t))return;ee.add(t),G.c.push(()=>{ee.delete(t),n&&(i&&t.d(1),n())}),t.o(e)}else n&&n()}function Je(t,e){z(t,1,1,()=>{e.delete(t.key)})}function Ke(t,e,i,n,s,u,r,o,a,l,h,p){let d=t.length,g=u.length,A=d;const b={};for(;A--;)b[t[A].key]=A;const F=[],c=new Map,v=new Map;for(A=g;A--;){const y=p(s,u,A),S=i(y);let P=r.get(S);P?n&&P.p(y,e):(P=l(S,y),P.c()),c.set(S,F[A]=P),S in b&&v.set(S,Math.abs(A-b[S]))}const $=new Set,M=new Set;function E(y){D(y,1),y.m(o,h),r.set(y.key,y),h=y.first,g--}for(;d&&g;){const y=F[g-1],S=t[d-1],P=y.key,B=S.key;y===S?(h=y.first,d--,g--):c.has(B)?!r.has(P)||$.has(P)?E(y):M.has(B)?d--:v.get(P)>v.get(B)?(M.add(P),E(y)):($.add(B),d--):(a(S,r),d--)}for(;d--;){const y=t[d];c.has(y.key)||a(y,r)}for(;g;)E(F[g-1]);return F}function pe(t){t&&t.c()}function te(t,e,i,n){const{fragment:s,after_update:u}=t.$$;s&&s.m(e,i),n||ue(()=>{const r=t.$$.on_mount.map(H).filter(be);t.$$.on_destroy?t.$$.on_destroy.push(...r):O(r),t.$$.on_mount=[]}),u.forEach(ue)}function ie(t,e){const i=t.$$;i.fragment!==null&&(O(i.on_destroy),i.fragment&&i.fragment.d(e),i.on_destroy=i.fragment=null,i.ctx=[])}function We(t,e){t.$$.dirty[0]===-1&&(Y.push(t),Ze(),t.$$.dirty.fill(0)),t.$$.dirty[e/31|0]|=1<<e%31}function me(t,e,i,n,s,u,r,o=[-1]){const a=V;Z(t);const l=t.$$={fragment:null,ctx:[],props:u,update:C,not_equal:s,bound:we(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(a?a.$$.context:[])),callbacks:we(),dirty:o,skip_bound:!1,root:e.target||a.$$.root};r&&r(l.root);let h=!1;if(l.ctx=i?i(t,e.props||{},(p,d,...g)=>{const A=g.length?g[0]:d;return l.ctx&&s(l.ctx[p],l.ctx[p]=A)&&(!l.skip_bound&&l.bound[p]&&l.bound[p](A),h&&We(t,p)),d}):[],l.update(),h=!0,O(l.before_update),l.fragment=n?n(l.ctx):!1,e.target){if(e.hydrate){const p=Oe(e.target);l.fragment&&l.fragment.l(p),p.forEach(N)}else l.fragment&&l.fragment.c();e.intro&&D(t.$$.fragment),te(t,e.target,e.anchor,e.customElement),$e()}Z(a)}class he{$destroy(){ie(this,1),this.$destroy=C}$on(e,i){if(!be(i))return C;const n=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return n.push(i),()=>{const s=n.indexOf(i);s!==-1&&n.splice(s,1)}}$set(e){this.$$set&&!Ie(e)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}}const U=[];function Pe(t,e=C){let i;const n=new Set;function s(o){if(K(t,o)&&(t=o,i)){const a=!U.length;for(const l of n)l[1](),U.push(l,t);if(a){for(let l=0;l<U.length;l+=2)U[l][0](U[l+1]);U.length=0}}}function u(o){s(o(t))}function r(o,a=C){const l=[o,a];return n.add(l),n.size===1&&(i=e(s)||C),o(t),()=>{n.delete(l),n.size===0&&(i(),i=null)}}return{set:s,update:u,subscribe:r}}const De=Pe({}),ne=Pe("absolute"),pt="";function je(t){let e,i,n,s,u,r,o,a,l,h,p,d,g,A,b,F,c,v,$,M,E,y,S,P,B,re=t[0].sampleCount+"",ve,Xe,le,Qe,oe,Ae,Ge;return{c(){e=_("div"),i=_("div"),n=_("div"),s=_("div"),s.textContent="pyinstrument",u=w(),r=_("div"),o=_("label"),o.textContent="Absolute time",a=w(),l=_("input"),h=w(),p=_("div"),p.textContent="Recorded:",d=w(),g=_("div"),g.textContent=`${t[2]}`,A=w(),b=_("div"),b.textContent="Duration:",F=w(),c=_("div"),c.textContent=`${t[4]} seconds`,v=w(),$=_("label"),$.textContent="Proportional time",M=w(),E=_("input"),y=w(),S=_("div"),S.textContent="Samples:",P=w(),B=_("div"),ve=L(re),Xe=w(),le=_("div"),le.textContent="CPU time:",Qe=w(),oe=_("div"),oe.textContent=`${t[3]} seconds`,m(s,"class","title svelte-tewt95"),m(o,"class","metric-label svelte-tewt95"),m(o,"for","absolute"),m(l,"type","radio"),l.__value="absolute",l.value=l.__value,m(l,"id","absolute"),m(l,"name","time-format"),l.checked=!0,t[6][0].push(l),m(p,"class","metric-label svelte-tewt95"),m(g,"class","metric-value svelte-tewt95"),m(b,"class","metric-label svelte-tewt95"),m(c,"class","metric-value svelte-tewt95"),m($,"class","metric-label svelte-tewt95"),m($,"for","proportion"),m(E,"type","radio"),E.__value="proportion",E.value=E.__value,m(E,"id","proportion"),m(E,"name","time-format"),t[6][0].push(E),m(S,"class","metric-label svelte-tewt95"),m(B,"class","metric-value svelte-tewt95"),m(le,"class","metric-label svelte-tewt95"),m(oe,"class","metric-value svelte-tewt95"),m(r,"class","metrics svelte-tewt95"),m(n,"class","row svelte-tewt95"),m(i,"class","margins"),m(e,"class","header svelte-tewt95")},m(I,J){X(I,e,J),f(e,i),f(i,n),f(n,s),f(n,u),f(n,r),f(r,o),f(r,a),f(r,l),l.checked=l.__value===t[1],f(r,h),f(r,p),f(r,d),f(r,g),f(r,A),f(r,b),f(r,F),f(r,c),f(r,v),f(r,$),f(r,M),f(r,E),E.checked=E.__value===t[1],f(r,y),f(r,S),f(r,P),f(r,B),f(B,ve),f(r,Xe),f(r,le),f(r,Qe),f(r,oe),Ae||(Ge=[W(l,"change",t[5]),W(E,"change",t[7])],Ae=!0)},p(I,[J]){J&2&&(l.checked=l.__value===I[1]),J&2&&(E.checked=E.__value===I[1]),J&1&&re!==(re=I[0].sampleCount+"")&&q(ve,re)},i:C,o:C,d(I){I&&N(e),t[6][0].splice(t[6][0].indexOf(l),1),t[6][0].splice(t[6][0].indexOf(E),1),Ae=!1,O(Ge)}}}function xe(t,e,i){var p;let n;se(t,ne,d=>i(1,n=d));let{session:s}=e;const u=new Date(s.startTime*1e3).toLocaleString(),r=(p=s.cpuTime)==null?void 0:p.toLocaleString(void 0,{maximumSignificantDigits:3}),o=s.duration.toLocaleString(void 0,{maximumSignificantDigits:3}),a=[[]];function l(){n=this.__value,ne.set(n)}function h(){n=this.__value,ne.set(n)}return t.$$set=d=>{"session"in d&&i(0,s=d.session)},[s,n,u,r,o,l,a,h]}class et extends he{constructor(e){super(),me(this,e,xe,je,K,{session:0})}}const mt="";function Me(t,e,i){const n=t.slice();return n[14]=e[i],n}function Le(t){let e,i,n,s,u,r,o,a,l,h,p,d,g,A,b,F;return{c(){e=_("div"),i=_("div"),n=ke("svg"),s=ke("path"),u=w(),r=_("div"),o=L(t[5]),a=w(),l=_("div"),h=L(t[4]),p=w(),d=_("div"),g=w(),A=_("div"),A.textContent=`${t[9]}`,m(s,"d","M.937-.016L5.793 4.84.937 9.696z"),m(s,"fill",t[7]),m(s,"fill-rule","evenodd"),m(s,"fill-opacity",".582"),m(n,"width","6"),m(n,"height","10"),m(i,"class","frame-triangle svelte-1aphj50"),Q(i,"rotate",t[2]),T(i,"visibility",t[0].children.length>0?"visible":"hidden"),m(r,"class","time svelte-1aphj50"),T(r,"color",t[7]),T(r,"font-weight",t[0].proportionOfTotal<.2?500:600),m(l,"class","name svelte-1aphj50"),m(d,"class","spacer"),T(d,"flex","1"),m(A,"class","code-position svelte-1aphj50"),m(e,"class","frame-description svelte-1aphj50"),Q(e,"application-code",t[0].isApplicationCode),Q(e,"children-visible",t[2]),T(e,"padding-left",`${t[1]*35}px`)},m(c,v){X(c,e,v),f(e,i),f(i,n),f(n,s),f(e,u),f(e,r),f(r,o),f(e,a),f(e,l),f(l,h),f(e,p),f(e,d),f(e,g),f(e,A),b||(F=W(e,"click",Ee(Ce(t[10]))),b=!0)},p(c,v){v&128&&m(s,"fill",c[7]),v&4&&Q(i,"rotate",c[2]),v&1&&T(i,"visibility",c[0].children.length>0?"visible":"hidden"),v&32&&q(o,c[5]),v&128&&T(r,"color",c[7]),v&1&&T(r,"font-weight",c[0].proportionOfTotal<.2?500:600),v&16&&q(h,c[4]),v&1&&Q(e,"application-code",c[0].isApplicationCode),v&4&&Q(e,"children-visible",c[2]),v&2&&T(e,"padding-left",`${c[1]*35}px`)},d(c){c&&N(e),b=!1,F()}}}function Ne(t){let e,i,n,s,u=t[0].group.frames.length+"",r,o,a,l,h,p;return{c(){e=_("div"),i=_("div"),n=_("div"),n.innerHTML='<svg width="6" height="10"><path d="M.937-.016L5.793 4.84.937 9.696z" fill="#FFF" fill-rule="evenodd" fill-opacity=".582"></path></svg>',s=w(),r=L(u),o=L(` frames hidden
        (`),a=L(t[6]),l=L(")"),m(n,"class","group-triangle svelte-1aphj50"),Q(n,"rotate",t[8]),m(i,"class","group-header-button svelte-1aphj50"),m(e,"class","group-header svelte-1aphj50"),T(e,"padding-left",`${t[1]*35}px`)},m(d,g){X(d,e,g),f(e,i),f(i,n),f(i,s),f(i,r),f(i,o),f(i,a),f(i,l),h||(p=W(e,"click",Ee(Ce(t[11]))),h=!0)},p(d,g){g&256&&Q(n,"rotate",d[8]),g&1&&u!==(u=d[0].group.frames.length+"")&&q(r,u),g&64&&q(a,d[6]),g&2&&T(e,"padding-left",`${d[1]*35}px`)},d(d){d&&N(e),h=!1,p()}}}function ze(t){let e=[],i=new Map,n,s,u=t[0].children;const r=o=>o[14].identifier;for(let o=0;o<u.length;o+=1){let a=Me(t,u,o),l=r(a);i.set(l,e[o]=Be(l,a))}return{c(){for(let o=0;o<e.length;o+=1)e[o].c();n=ye()},m(o,a){for(let l=0;l<e.length;l+=1)e[l].m(o,a);X(o,n,a),s=!0},p(o,a){a&11&&(u=o[0].children,ce(),e=Ke(e,a,r,1,o,u,i,n.parentNode,Je,Be,n,Me),de())},i(o){if(!s){for(let a=0;a<u.length;a+=1)D(e[a]);s=!0}},o(o){for(let a=0;a<e.length;a+=1)z(e[a]);s=!1},d(o){for(let a=0;a<e.length;a+=1)e[a].d(o);o&&N(n)}}}function Be(t,e){let i,n,s;return n=new He({props:{frame:e[14],indent:e[1]+(e[3]?1:0)}}),{key:t,first:null,c(){i=ye(),pe(n.$$.fragment),this.first=i},m(u,r){X(u,i,r),te(n,u,r),s=!0},p(u,r){e=u;const o={};r&1&&(o.frame=e[14]),r&10&&(o.indent=e[1]+(e[3]?1:0)),n.$set(o)},i(u){s||(D(n.$$.fragment,u),s=!0)},o(u){z(n.$$.fragment,u),s=!1},d(u){u&&N(i),ie(n,u)}}}function tt(t){let e,i,n,s,u,r,o=t[3]&&Le(t),a=t[0].group&&t[0].group.rootFrame==t[0]&&t[2]&&Ne(t),l=t[2]&&ze(t);return{c(){e=_("div"),o&&o.c(),i=w(),a&&a.c(),n=w(),l&&l.c(),s=w(),u=_("div"),m(u,"class","visual-guide"),T(u,"left",`${t[1]*35+21}px`),T(u,"backgroundColor",t[7]),m(e,"class","frame svelte-1aphj50")},m(h,p){X(h,e,p),o&&o.m(e,null),f(e,i),a&&a.m(e,null),f(e,n),l&&l.m(e,null),f(e,s),f(e,u),r=!0},p(h,[p]){h[3]?o?o.p(h,p):(o=Le(h),o.c(),o.m(e,i)):o&&(o.d(1),o=null),h[0].group&&h[0].group.rootFrame==h[0]&&h[2]?a?a.p(h,p):(a=Ne(h),a.c(),a.m(e,n)):a&&(a.d(1),a=null),h[2]?l?(l.p(h,p),p&4&&D(l,1)):(l=ze(h),l.c(),D(l,1),l.m(e,s)):l&&(ce(),z(l,1,1,()=>{l=null}),de()),p&2&&T(u,"left",`${h[1]*35+21}px`),p&128&&T(u,"backgroundColor",h[7])},i(h){r||(D(l),r=!0)},o(h){z(l),r=!1},d(h){h&&N(e),o&&o.d(),a&&a.d(),l&&l.d()}}}function it(t,e,i){let n,s,u;se(t,De,c=>i(12,s=c)),se(t,ne,c=>i(13,u=c));let{frame:r}=e,{indent:o=0}=e,a=!0,l,h;r.className?h=`${r.className}.${r.function}`:h=r.function;const p=`${r.filePathShort}:${r.lineNo.toString().padEnd(4,"\xA0")}`;let d,g=null;if(r.group){const c=r.group.libraries;c.length<4?g=c.join(", "):g=`${c[0]}, ${c[1]}, ${c[2]}...`}let A;r.proportionOfTotal>.6?A="#FF4159":r.proportionOfTotal>.3?A="#F5A623":r.proportionOfTotal>.2?A="#D8CB2A":A="#7ED321";function b(){i(2,a=!a)}function F(){De.update(c=>{var v;return{...c,[(v=r.groupId)!=null?v:""]:!n}})}return t.$$set=c=>{"frame"in c&&i(0,r=c.frame),"indent"in c&&i(1,o=c.indent)},t.$$.update=()=>{var c,v,$;if(t.$$.dirty&4097&&(r.group?s[(c=r.groupId)!=null?c:""]||((v=r.group)==null?void 0:v.rootFrame)===r||r.children.filter(M=>!M.group).length>1?i(3,l=!0):i(3,l=!1):i(3,l=!0)),t.$$.dirty&8193)if(u==="absolute")i(5,d=r.time.toLocaleString(void 0,{minimumFractionDigits:3,maximumFractionDigits:3}));else if(u==="proportion")i(5,d=`${(r.proportionOfTotal*100).toLocaleString(void 0,{minimumFractionDigits:1,maximumFractionDigits:1})}%`);else throw new Error("unknown timeFormat");t.$$.dirty&4097&&i(8,n=s[($=r.groupId)!=null?$:""]===!0)},[r,o,a,l,h,d,g,A,n,p,b,F,s,u]}class He extends he{constructor(e){super(),me(this,e,it,tt,K,{frame:0,indent:1})}}const nt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAIKADAAQAAAABAAAAIAAAAACPTkDJAAAFPklEQVRYCdWXu4skVRSHb3VXP2bcXUVMBkRMTVww2XQDExEEH4iKiUYGhuJEPgJhUSMzQQSDRVzETNDMTTfUwL/ARUwM1t3pV1WX33eqbnfP9MxOu5kHTt9T93F+v3vOubeqi7QhT1+58vhiVn1G19WiaQ6KIqVQOmyT+h9kuUxp2bAAtQkpij9pbw5G5fu/3br1x8ql4NW0+rVJzaN29nop9VUeCtoerXpMTj53g+AGcINR1YB3RIJMN6dIxd/luLxcZoftzltwAct+SiNGh2jJ84BnSQWJTeBNW2C0ZrvufrZA5y2JmuemWkfCjYq5IsC6q2iE2V0LuDdMaYxKZDxo+yS3JZkEY+5SsAU7P5q1qZNEAxnXGo0NubomQM4dWPnCMAU/fUCnoA7kFjPEPohuqfO6uc++0xKSVHUcHDbNwZpAeFyHyMcg408G16mSnwW3b5OEz3rtCAyJnHUQUSEFmMdki0CMmsNOX/gkpf1RSpf223Q43jC2MMw4tFWsjwHzBngsIOT6CaEf7wGKrTo/agFbH8oWAQdkWTF5htGntSDn2h2YBTal/9603Z2++swZ0+5BZIjdQwfUz5DcGwHVotSfdpYtAg7oUKzaycyo6PjmkA4cRmgBuf5zSj/80kbAuToaYHz9afdgCiCxJF3gpudfZampOyGnEwAwIkEbuROBXEauXZGdYzrEtFTTpx3zHGeewGoFcFxk2Cdlxempy8/oZyXm1KO3R14fIraPXEzpAnXgyVBmRMdjNgdVsqZgxLw9cl4C7m7N+d17pOqIuaj2LKetQzs1Ai3E+reRJip4CSnz2wPYHC9wFL66MYko9k8pwgmAU4hqbxZhO6tNXba321V8Uvruc4ZzGgQxErnFNszqItqC0DdpTtzfeqWJ21DwpRp5WkOdHwFJqM4UMLcdeB2AADPUkigSm00VOZixsOL8WfUB3oWd4ZWcT8CpgLz2XkoPX+IgEIU+q6K6QZqzqxoAr15vupqC8Air0wm7JwUL2AWJByIAuDXQEGZ1aegRASY4Ppq0AOZZIoZYsLh4zDsa4PSfeA+En/tGQMI5ZTe+xClhN+Q5z4b67TfZJeATiAQw4J4KwTzCOfT2nSb3JRALAJTEfKPQcrGBG5GwwCLMRCRAXcgiQY1IYD8ogbzuDd5qF7gL+p4ESM05Z//coWXH1oEE5oQkQPOi3EroDDk/Ai40Cp3SRE2AG+8MWyOk5h1j7ixnEogPD0dVcv/FVwXXLceKs20K5rCaFk06fKmOD424akkTXUFkVwanEtBZfH7hMK5eCEwBl0DNYHvJ0Idd4MHXr7djkGDtWQV3GqlTCRhybzqv1R4zbA/fXabRfhFAHkvflJO77XivuyElbRHG+h3yL6EtAu4iPkoF5q4veRn1ecEUAwYg0pSyQwAoRk3qz4p4DS+wawpxSVHoY9cobBHQtxBBBMBrN/rkvOEC6lF0hF/nKvaS31yE155btLVAFKIidbSDbBMI9HaluzjC4ZKQWHieBIcVPqvbyg/b503Dh91kTcB/LH4Z6wmNI8X2PnyxTkPSUI7JP7MjvE5hTrwDuO+rLvT2uXxnAXNNgL9LLHxdB16jOi24XgOEK68EyMKMb/uYRERol1w+FmRN67XrWonsKDdzRNPmXzPjbEXHxwWttsCcwpXki8cte91afP7zkdQuYch/zSizVv66ffvOwZNPfLus/YNSPMYuLkZIda66yw3NzwHMeCOBXcBNdVH8OBiXLx/7c5qJrFrK/GOYfETH97Sr/jOM38FnvodDGv8f+Rcj1XBLIcDKAQAAAABJRU5ErkJggg==",ht="";function rt(t){let e;return{c(){e=_("div"),e.textContent="No samples recorded.",m(e,"class","error")},m(i,n){X(i,e,n)},p:C,i:C,o:C,d(i){i&&N(e)}}}function lt(t){let e,i;return e=new He({props:{frame:t[0].rootFrame}}),{c(){pe(e.$$.fragment)},m(n,s){te(e,n,s),i=!0},p(n,s){const u={};s&1&&(u.frame=n[0].rootFrame),e.$set(u)},i(n){i||(D(e.$$.fragment,n),i=!0)},o(n){z(e.$$.fragment,n),i=!1},d(n){ie(e,n)}}}function ot(t){let e,i,n,s,u,r,o,a,l,h,p,d,g;i=new et({props:{session:t[0]}});const A=[lt,rt],b=[];function F(c,v){return c[0].rootFrame?0:1}return p=F(t),d=b[p]=A[p](t),{c(){e=_("div"),pe(i.$$.fragment),n=w(),s=_("div"),u=w(),r=_("div"),o=_("div"),a=_("span"),a.textContent="Program:\xA0",l=L(t[1]),h=w(),d.c(),m(s,"class","spacer"),T(s,"height","20px"),m(a,"class","label svelte-1gm5pcf"),m(o,"class","program svelte-1gm5pcf"),m(r,"class","margins"),m(e,"class","app svelte-1gm5pcf")},m(c,v){X(c,e,v),te(i,e,null),f(e,n),f(e,s),f(e,u),f(e,r),f(r,o),f(o,a),f(o,l),f(r,h),b[p].m(r,null),g=!0},p(c,[v]){const $={};v&1&&($.session=c[0]),i.$set($),(!g||v&2)&&q(l,c[1]);let M=p;p=F(c),p===M?b[p].p(c,v):(ce(),z(b[M],1,1,()=>{b[M]=null}),de(),d=b[p],d?d.p(c,v):(d=b[p]=A[p](c),d.c()),D(d,1),d.m(r,null))},i(c){g||(D(i.$$.fragment,c),D(d),g=!0)},o(c){z(i.$$.fragment,c),z(d),g=!1},d(c){c&&N(e),ie(i),b[p].d()}}}function ge(){document.body.style.minHeight=`${window.scrollY+window.innerHeight}px`}function st(t,e,i){let{session:n}=e;const s=document.createElement("link");s.rel="shortut icon",s.href=nt,document.head.appendChild(s),qe(()=>{window.addEventListener("scroll",ge),ge()}),Ue(()=>{window.removeEventListener("scroll",ge)});const u=n.rootFrame,r=u==null?void 0:u.time.toLocaleString(void 0,{maximumSignificantDigits:3});let o=u==null?void 0:u.function;return o=="<module>"&&(o=n.program),document.title=`${r}s - ${o} - pyinstrument`,t.$$set=a=>{"session"in a&&i(0,n=a.session)},[n,o]}class at extends he{constructor(e){super(),me(this,e,st,ot,K,{session:0})}}class ut{constructor(e,i){k(this,"id");k(this,"rootFrame");k(this,"frames",[]);this.id=e,this.rootFrame=i}addFrame(e){this.frames.push(e)}get exitFrames(){const e=[];for(const i of this.frames){let n=!1;for(const s of i.children)if(s.group!=this){n=!0;break}n&&e.push(i)}return e}get libraries(){const e=[];for(const i of this.frames){const n=/^[^\\/.]*/.exec(i.filePathShort)[0];e.includes(n)||e.push(n)}return e}}class _e{constructor(e,i=null,n={groups:{}}){k(this,"function");k(this,"filePath");k(this,"filePathShort");k(this,"lineNo");k(this,"time");k(this,"totalTime");k(this,"awaitTime");k(this,"isApplicationCode");k(this,"groupId");k(this,"className");k(this,"parent");k(this,"children");k(this,"group");var s,u;if(this.parent=i,this.function=e.function,this.filePath=e.file_path,this.filePathShort=e.file_path_short,this.lineNo=e.line_no,this.time=e.time,this.totalTime=this.parent?this.parent.totalTime:this.time,this.awaitTime=e.await_time,this.isApplicationCode=e.is_application_code,this.groupId=(s=e.group_id)!=null?s:null,this.className=(u=e.class_name)!=null?u:null,e.group_id){const r=e.group_id;let o=n.groups[r];o||(o=n.groups[r]=new ut(r,this)),o.addFrame(this),this.group=n.groups[r]}else this.group=null;this.children=e.children.map(r=>new _e(r,this,n))}get identifier(){return`${this.function}:${this.filePath}:${this.lineNo}`}get proportionOfTotal(){return this.time/this.totalTime}}class ft{constructor(e){k(this,"startTime");k(this,"duration");k(this,"sampleCount");k(this,"program");k(this,"cpuTime");k(this,"rootFrame");this.startTime=e.start_time,this.duration=e.duration,this.sampleCount=e.sample_count,this.program=e.program,this.cpuTime=e.cpu_time,this.rootFrame=e.root_frame?new _e(e.root_frame):null}}return{render(t,e){const i=new ft(e);return new at({target:t,props:{session:i}})}}}();
</script>
                <style>@import"https://fonts.googleapis.com/css?family=Source+Code+Pro:400,600|Source+Sans+Pro:400,600";html,body{background-color:#303538;color:#fff;padding:0;margin:0}.margins{padding:0 30px}.header.svelte-tewt95{background:#292f32}.row.svelte-tewt95{display:flex;align-items:center}.title.svelte-tewt95{font-size:34px;padding-top:20px;padding-bottom:16px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-right:10px;flex:1}.metrics.svelte-tewt95{display:grid;grid-template-columns:auto auto auto auto auto auto;font-size:14px;text-transform:uppercase;grid-gap:1px 8px}.metric-label.svelte-tewt95{font-weight:600;color:#a9abad}.metric-value.svelte-tewt95{color:#737779;margin-right:.5em}.frame.svelte-1aphj50.svelte-1aphj50{font-family:Source Code Pro,Roboto Mono,Consolas,Monaco,monospace;font-size:15px;z-index:0;position:relative;user-select:none}.group-header.svelte-1aphj50.svelte-1aphj50{margin-left:35px}.group-header-button.svelte-1aphj50.svelte-1aphj50{display:inline-block;color:#ffffff94;user-select:none;cursor:default;position:relative}.group-header-button.svelte-1aphj50.svelte-1aphj50:before{position:absolute;left:-3px;right:-3px;top:0px;bottom:-1px;content:"";z-index:-1;background-color:#3b4043}.group-header-button.svelte-1aphj50.svelte-1aphj50:hover:before{background-color:#4a4f54}.group-triangle.svelte-1aphj50.svelte-1aphj50,.frame-triangle.svelte-1aphj50.svelte-1aphj50{width:6px;height:10px;padding-left:6px;padding-right:5px;display:inline-block}.group-triangle.rotate.svelte-1aphj50.svelte-1aphj50,.frame-triangle.rotate.svelte-1aphj50.svelte-1aphj50{transform:translate(6px,4px) rotate(90deg)}.frame-description.svelte-1aphj50.svelte-1aphj50{display:flex}.frame-description.svelte-1aphj50.svelte-1aphj50:hover:before{position:absolute;left:-3px;right:-3px;top:-1px;height:22px;content:"";z-index:-1;background-color:#354759;opacity:.5}.frame-triangle.svelte-1aphj50.svelte-1aphj50{opacity:1}.frame-description.children-visible.svelte-1aphj50 .frame-triangle.svelte-1aphj50{opacity:0}.frame-description.children-visible.svelte-1aphj50:hover .frame-triangle.svelte-1aphj50{opacity:1}.name.svelte-1aphj50.svelte-1aphj50,.time.svelte-1aphj50.svelte-1aphj50,.code-position.svelte-1aphj50.svelte-1aphj50{user-select:text;cursor:default}.application-code.svelte-1aphj50 .name.svelte-1aphj50{color:#5db3ff}.time.svelte-1aphj50.svelte-1aphj50{margin-right:.55em;color:#b8e98685}.code-position.svelte-1aphj50.svelte-1aphj50{color:#ffffff80;text-align:right;margin-left:1em}.visual-guide{top:21px;bottom:0;left:0;width:2px;background-color:#fff;position:absolute;opacity:.08}.frame-description:hover~.visual-guide{opacity:.4}.frame-description:hover~.children .visual-guide{opacity:.1}.app.svelte-1gm5pcf.svelte-1gm5pcf{font-family:Source Sans Pro,Helvetica,Arial,sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.program.svelte-1gm5pcf.svelte-1gm5pcf{font-size:14px;font-weight:600;margin-bottom:16px;color:#b4b4b4}.program.svelte-1gm5pcf .label.svelte-1gm5pcf{color:#eaeaea;text-transform:uppercase}
</style>

                <script>
                    const sessionData = {"start_time": 1701399463.495920,"duration": 1.409879,"sample_count": 428,"program": "proxy_cli.py --config config.yaml --debug","cpu_time": 0.517319,"root_frame": {"function": "_run_once","file_path_short": "asyncio/base_events.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py","line_no": 1806,"time": 1.409662,"await_time": 1.291243,"is_application_code": false,"children": [{"function": "_run","file_path_short": "asyncio/events.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/events.py","line_no": 78,"time": 1.406484,"await_time": 1.291243,"is_application_code": false,"children": [{"function": "coro","file_path_short": "starlette/middleware/base.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/starlette/middleware/base.py","line_no": 65,"time": 1.292728,"await_time": 1.267557,"is_application_code": false,"children": [{"function": "__call__","file_path_short": "starlette/middleware/cors.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/starlette/middleware/cors.py","line_no": 73,"time": 1.292728,"await_time": 1.267557,"is_application_code": false,"children": [{"function": "__call__","file_path_short": "starlette/middleware/exceptions.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/starlette/middleware/exceptions.py","line_no": 53,"time": 1.292728,"await_time": 1.267557,"is_application_code": false,"children": [{"function": "__call__","file_path_short": "fastapi/middleware/asyncexitstack.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py","line_no": 12,"time": 1.292728,"await_time": 1.267557,"is_application_code": false,"children": [{"function": "__call__","file_path_short": "starlette/routing.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/starlette/routing.py","line_no": 697,"time": 1.292728,"await_time": 1.267557,"is_application_code": false,"children": [{"function": "handle","file_path_short": "starlette/routing.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/starlette/routing.py","line_no": 265,"time": 1.292728,"await_time": 1.267557,"is_application_code": false,"children": [{"function": "app","file_path_short": "starlette/routing.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/starlette/routing.py","line_no": 63,"time": 1.292728,"await_time": 1.267557,"is_application_code": false,"children": [{"function": "app","file_path_short": "fastapi/routing.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/fastapi/routing.py","line_no": 217,"time": 1.292728,"await_time": 1.267557,"is_application_code": false,"children": [{"function": "run_endpoint_function","file_path_short": "fastapi/routing.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/fastapi/routing.py","line_no": 182,"time": 1.290719,"await_time": 1.267557,"is_application_code": false,"children": [{"function": "embeddings","file_path_short": "proxy_server.py","file_path": "/Users/<USER>/Github/litellm/litellm/proxy/proxy_server.py","line_no": 797,"time": 1.290719,"await_time": 1.267557,"is_application_code": true,"children": [{"function": "aembedding","file_path_short": "litellm/router.py","file_path": "/Users/<USER>/Github/litellm/litellm/router.py","line_no": 310,"time": 1.288674,"await_time": 1.267557,"is_application_code": true,"children": [{"function": "aembedding","file_path_short": "litellm/main.py","file_path": "/Users/<USER>/Github/litellm/litellm/main.py","line_no": 1680,"time": 1.285234,"await_time": 1.267557,"is_application_code": true,"children": [{"function": "aembedding","file_path_short": "litellm/llms/openai.py","file_path": "/Users/<USER>/Github/litellm/litellm/llms/openai.py","line_no": 326,"time": 1.262861,"await_time": 1.246184,"is_application_code": true,"children": [{"function": "create","file_path_short": "openai/resources/embeddings.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/openai/resources/embeddings.py","line_no": 126,"time": 1.261860,"await_time": 1.246184,"is_application_code": false,"children": [{"function": "post","file_path_short": "openai/_base_client.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/openai/_base_client.py","line_no": 1460,"time": 1.259861,"await_time": 1.246184,"is_application_code": false,"children": [{"function": "request","file_path_short": "openai/_base_client.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/openai/_base_client.py","line_no": 1266,"time": 1.259861,"await_time": 1.246184,"is_application_code": false,"children": [{"function": "_request","file_path_short": "openai/_base_client.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/openai/_base_client.py","line_no": 1283,"time": 1.259861,"await_time": 1.246184,"is_application_code": false,"children": [{"function": "send","file_path_short": "httpx/_client.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/httpx/_client.py","line_no": 1584,"time": 1.258751,"await_time": 1.246184,"is_application_code": false,"children": [{"function": "_send_handling_auth","file_path_short": "httpx/_client.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/httpx/_client.py","line_no": 1633,"time": 1.213750,"await_time": 1.203184,"is_application_code": false,"children": [{"function": "_send_handling_redirects","file_path_short": "httpx/_client.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/httpx/_client.py","line_no": 1667,"time": 1.213750,"await_time": 1.203184,"is_application_code": false,"children": [{"function": "_send_single_request","file_path_short": "httpx/_client.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/httpx/_client.py","line_no": 1705,"time": 1.213750,"await_time": 1.203184,"is_application_code": false,"children": [{"function": "handle_async_request","file_path_short": "httpx/_transports/default.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/httpx/_transports/default.py","line_no": 334,"time": 1.213750,"await_time": 1.203184,"is_application_code": false,"children": [{"function": "handle_async_request","file_path_short": "httpcore/_async/connection_pool.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/httpcore/_async/connection_pool.py","line_no": 206,"time": 1.213750,"await_time": 1.203184,"is_application_code": false,"children": [{"function": "handle_async_request","file_path_short": "httpcore/_async/connection.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/httpcore/_async/connection.py","line_no": 60,"time": 1.213750,"await_time": 1.203184,"is_application_code": false,"children": [{"function": "handle_async_request","file_path_short": "httpcore/_async/http11.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/httpcore/_async/http11.py","line_no": 70,"time": 1.172967,"await_time": 1.170933,"is_application_code": false,"children": [{"function": "_receive_response_headers","file_path_short": "httpcore/_async/http11.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/httpcore/_async/http11.py","line_no": 157,"time": 1.171934,"await_time": 1.170933,"is_application_code": false,"children": [{"function": "_receive_event","file_path_short": "httpcore/_async/http11.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/httpcore/_async/http11.py","line_no": 192,"time": 1.171934,"await_time": 1.170933,"is_application_code": false,"children": [{"function": "read","file_path_short": "httpcore/_backends/anyio.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/httpcore/_backends/anyio.py","line_no": 23,"time": 1.170933,"await_time": 1.170933,"is_application_code": false,"children": [{"function": "receive","file_path_short": "anyio/streams/tls.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/anyio/streams/tls.py","line_no": 195,"time": 1.170933,"await_time": 1.170933,"is_application_code": false,"children": [{"function": "_call_sslobject_method","file_path_short": "anyio/streams/tls.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/anyio/streams/tls.py","line_no": 126,"time": 1.170933,"await_time": 1.170933,"is_application_code": false,"children": [{"function": "receive","file_path_short": "anyio/_backends/_asyncio.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/anyio/_backends/_asyncio.py","line_no": 1194,"time": 1.170933,"await_time": 1.170933,"is_application_code": false,"children": [{"function": "wait","file_path_short": "asyncio/locks.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/locks.py","line_no": 200,"time": 1.170933,"await_time": 1.170933,"is_application_code": false,"children": [{"function": "[await]","file_path_short": "asyncio/locks.py","file_path": "","line_no": 0,"time": 1.170933,"await_time": 1.170933,"is_application_code": false,"children": [],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "Event"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "SocketStream"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "TLSStream"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "TLSStream"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AnyIOStream"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AsyncHTTP11Connection"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AsyncHTTP11Connection"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AsyncHTTP11Connection"},{"function": "_connect","file_path_short": "httpcore/_async/connection.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/httpcore/_async/connection.py","line_no": 98,"time": 0.040783,"await_time": 0.032250,"is_application_code": false,"children": [{"function": "start_tls","file_path_short": "httpcore/_backends/anyio.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/httpcore/_backends/anyio.py","line_no": 56,"time": 0.035735,"await_time": 0.032250,"is_application_code": false,"children": [{"function": "wrap","file_path_short": "anyio/streams/tls.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/anyio/streams/tls.py","line_no": 70,"time": 0.034735,"await_time": 0.032250,"is_application_code": false,"children": [{"function": "_call_sslobject_method","file_path_short": "anyio/streams/tls.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/anyio/streams/tls.py","line_no": 126,"time": 0.034735,"await_time": 0.032250,"is_application_code": false,"children": [{"function": "receive","file_path_short": "anyio/_backends/_asyncio.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/anyio/_backends/_asyncio.py","line_no": 1194,"time": 0.032250,"await_time": 0.032250,"is_application_code": false,"children": [{"function": "wait","file_path_short": "asyncio/locks.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/locks.py","line_no": 200,"time": 0.032250,"await_time": 0.032250,"is_application_code": false,"children": [{"function": "[await]","file_path_short": "asyncio/locks.py","file_path": "","line_no": 0,"time": 0.032250,"await_time": 0.032250,"is_application_code": false,"children": [],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "Event"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "SocketStream"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "TLSStream"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "TLSStream"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AnyIOStream"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AsyncHTTPConnection"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AsyncHTTPConnection"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AsyncConnectionPool"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AsyncHTTPTransport"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AsyncClient"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AsyncClient"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AsyncClient"},{"function": "aread","file_path_short": "httpx/_models.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/httpx/_models.py","line_no": 904,"time": 0.045001,"await_time": 0.043000,"is_application_code": false,"children": [{"function": "<listcomp>","file_path_short": "httpx/_models.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/httpx/_models.py","line_no": 909,"time": 0.045001,"await_time": 0.043000,"is_application_code": false,"children": [{"function": "[await]","file_path_short": "httpx/_models.py","file_path": "","line_no": 0,"time": 0.043000,"await_time": 0.043000,"is_application_code": false,"children": [],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "Response"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AsyncClient"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AsyncOpenAI"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AsyncOpenAI"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AsyncOpenAI"}],"group_id": "266ffe36-18ff-4940-a539-bf9ebf805062","class_name": "AsyncEmbeddings"}],"class_name": "OpenAIChatCompletion"},{"function": "[await]","file_path_short": "litellm/main.py","file_path": "","line_no": 0,"time": 0.021373,"await_time": 0.021373,"is_application_code": false,"children": []}]}],"class_name": "Router"}]}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92","class_name": "APIRoute"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92","class_name": "APIRouter"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92","class_name": "AsyncExitStackMiddleware"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92","class_name": "ExceptionMiddleware"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92","class_name": "CORSMiddleware"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92"},{"function": "run_asgi","file_path_short": "uvicorn/protocols/http/h11_impl.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py","line_no": 406,"time": 0.086022,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "__call__","file_path_short": "uvicorn/middleware/proxy_headers.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py","line_no": 48,"time": 0.086022,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "__call__","file_path_short": "fastapi/applications.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/fastapi/applications.py","line_no": 289,"time": 0.086022,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "__call__","file_path_short": "starlette/applications.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/starlette/applications.py","line_no": 118,"time": 0.086022,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "__call__","file_path_short": "starlette/middleware/errors.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/starlette/middleware/errors.py","line_no": 147,"time": 0.086022,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "__call__","file_path_short": "starlette/middleware/base.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/starlette/middleware/base.py","line_no": 24,"time": 0.086022,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "profile_request","file_path_short": "proxy_server.py","file_path": "/Users/<USER>/Github/litellm/litellm/proxy/proxy_server.py","line_no": 121,"time": 0.086022,"await_time": 0.000000,"is_application_code": true,"children": [{"function": "output_html","file_path_short": "pyinstrument/profiler.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/profiler.py","line_no": 303,"time": 0.086022,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "output","file_path_short": "pyinstrument/profiler.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/profiler.py","line_no": 327,"time": 0.086022,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render","file_path_short": "pyinstrument/renderers/html.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/html.py","line_no": 28,"time": 0.086022,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_json","file_path_short": "pyinstrument/renderers/html.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/html.py","line_no": 87,"time": 0.086022,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 64,"time": 0.086022,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "root_frame","file_path_short": "pyinstrument/session.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/session.py","line_no": 114,"time": 0.039019,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "build_frame_tree","file_path_short": "pyinstrument/frame_ops.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/frame_ops.py","line_no": 19,"time": 0.039019,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "record_time_from_frame_info","file_path_short": "pyinstrument/frame.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/frame.py","line_no": 83,"time": 0.017008,"await_time": 0.000000,"is_application_code": false,"children": [],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "Frame"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "Session"},{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.035000,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.034000,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.033000,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.033000,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.028999,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.028999,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.027999,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.026999,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.026999,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.024999,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.024999,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.024999,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.023999,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.023000,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.021000,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.021000,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.020000,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.019000,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.019000,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.017000,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.016000,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.015000,"await_time": 0.000000,"is_application_code": false,"children": [{"function": "render_frame","file_path_short": "pyinstrument/renderers/jsonrenderer.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/pyinstrument/renderers/jsonrenderer.py","line_no": 33,"time": 0.015000,"await_time": 0.000000,"is_application_code": false,"children": [],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "JSONRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "HTMLRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "HTMLRenderer"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "Profiler"}],"group_id": "d9c80c1a-925b-4b35-b4ef-1b7eab091619","class_name": "Profiler"}]}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92","class_name": "dict"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92","class_name": "ServerErrorMiddleware"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92","class_name": "FastAPI"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92","class_name": "FastAPI"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92","class_name": "ProxyHeadersMiddleware"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92","class_name": "RequestResponseCycle"},{"function": "try_connect","file_path_short": "anyio/_core/_sockets.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/anyio/_core/_sockets.py","line_no": 164,"time": 0.024724,"await_time": 0.023686,"is_application_code": false,"children": [{"function": "connect_tcp","file_path_short": "anyio/_backends/_asyncio.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/anyio/_backends/_asyncio.py","line_no": 1622,"time": 0.024724,"await_time": 0.023686,"is_application_code": false,"children": [{"function": "create_connection","file_path_short": "asyncio/base_events.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py","line_no": 966,"time": 0.024724,"await_time": 0.023686,"is_application_code": false,"children": [{"function": "_connect_sock","file_path_short": "asyncio/base_events.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py","line_no": 930,"time": 0.024724,"await_time": 0.023686,"is_application_code": false,"children": [{"function": "sock_connect","file_path_short": "asyncio/selector_events.py","file_path": "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py","line_no": 486,"time": 0.024724,"await_time": 0.023686,"is_application_code": false,"children": [{"function": "[await]","file_path_short": "asyncio/selector_events.py","file_path": "","line_no": 0,"time": 0.023686,"await_time": 0.023686,"is_application_code": false,"children": [],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92","class_name": "_UnixSelectorEventLoop"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92","class_name": "_UnixSelectorEventLoop"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92","class_name": "_UnixSelectorEventLoop"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92"}],"group_id": "966b2cf6-9032-48f6-8a54-d9218fd7bd92","class_name": "Handle"}],"class_name": "_UnixSelectorEventLoop"}}
;
                    pyinstrumentHTMLRenderer.render(document.getElementById('app'), sessionData);
                </script>
            </body>
            </html>
        