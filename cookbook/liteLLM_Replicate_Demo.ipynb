{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {"id": "YV6L5fNv7Kep"}, "source": ["# Call Replicate LLMs using chatGPT Input/Output Format\n", "This tutorial covers using the following Replicate Models with liteLLM\n", "\n", "- [StableLM Tuned Alpha 7B](https://replicate.com/stability-ai/stablelm-tuned-alpha-7b)\n", "- [LLAMA-2 70B Chat](https://replicate.com/replicate/llama-2-70b-chat)\n", "- [A16z infra-LLAMA-2 7B Chat](https://replicate.com/a16z-infra/llama-2-7b-chat)\n", "- [Dolly V2 12B](https://replicate.com/replicate/dolly-v2-12b)\n", "- [Vicuna 13B](https://replicate.com/replicate/vicuna-13b)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "TO-EdF84O9QT"}, "outputs": [], "source": ["# install liteLLM\n", "!pip install litellm"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "mpHTbTqQ8fey"}, "source": ["Imports & Set ENV variables\n", "Get your Replicate Key: https://replicate.com/account/api-tokens"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "kDbgfcU8O-dW"}, "outputs": [], "source": ["from litellm import completion\n", "import os\n", "os.environ['REPLICATE_API_TOKEN'] = ' ' # @param\n", "user_message = \"Hello, whats the weather in San Francisco??\"\n", "messages = [{ \"content\": user_message,\"role\": \"user\"}]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "1KmkOdzLSOmJ"}, "source": ["## Call Replicate Models using completion(model, messages) - chatGPT format"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XJ4nh4SnRzHP", "outputId": "986c0544-bb40-4915-f00f-498b0e518307"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["replicate is not installed. Installing...\n", "Response from stability-ai/stablelm-tuned-alpha-7b:c49dae362cbaecd2ceabb5bd34fdb68413c4ff775111fea065d259d577757beb \n", "]\n", "\n", "{'choices': [{'finish_reason': 'stop', 'index': 0, 'message': {'role': 'assistant', 'content': \"I'm sorry for you being unable to access this content as my training data only goes up until 2023/03. However I can tell you what your local weather forecast may look like at any time of year with respect to current conditions:\"}}], 'created': 1691611730.7224207, 'model': 'stability-ai/stablelm-tuned-alpha-7b:c49dae362cbaecd2ceabb5bd34fdb68413c4ff775111fea065d259d577757beb', 'usage': {'prompt_tokens': 9, 'completion_tokens': 49, 'total_tokens': 58}}\n", "Response from replicate/llama-2-70b-chat:2c1608e18606fad2812020dc541930f2d0495ce32eee50074220b87300bc16e1 \n", "]\n", "\n", "{'choices': [{'finish_reason': 'stop', 'index': 0, 'message': {'role': 'assistant', 'content': \" Hello! I'm happy to help you with your question. However, I must point out that the question itself may not be meaningful. San Francisco is a city located in California, USA, and it is not possible for me to provide you with the current weather conditions there as I am a text-based AI language model and do not have access to real-time weather data. Additionally, the weather in San Francisco can vary greatly depending on the time of year, so it would be best to check a reliable weather source for the most up-to-date information.\\n\\nIf you meant to ask a different question, please feel free to rephrase it, and I will do my best to assist you in a safe and positive manner.\"}}], 'created': 1691611745.0269957, 'model': 'replicate/llama-2-70b-chat:2c1608e18606fad2812020dc541930f2d0495ce32eee50074220b87300bc16e1', 'usage': {'prompt_tokens': 9, 'completion_tokens': 143, 'total_tokens': 152}}\n", "Response from a16z-infra/llama-2-7b-chat:4f0b260b6a13eb53a6b1891f089d57c08f41003ae79458be5011303d81a394dc \n", "]\n", "\n", "{'choices': [{'finish_reason': 'stop', 'index': 0, 'message': {'role': 'assistant', 'content': \" Hello! I'm here to help you with your question. However, I must inform you that the weather in San Francisco can be quite unpredictable and can change rapidly. It's important to check reliable sources such as AccuWeather or the National Weather Service for the most up-to-date and accurate information about the weather in San Francisco.\\nI cannot provide you with real-time weather data or forecasts as I'm just an AI and do not have access to current weather conditions or predictions. But I can suggest some trustworthy websites or apps where you can find the latest weather updates:\\n* AccuWeather (accuweather.com)\\n* The Weather Channel (weather.com)\\n* Dark Sky (darksky.net)\\n* Weather Underground (wunderground.com)\\nRemember, it's always best to consult multiple sources for the most accurate information when planning your day or trip. Enjoy your day!\"}}], 'created': 1691611748.7723358, 'model': 'a16z-infra/llama-2-7b-chat:4f0b260b6a13eb53a6b1891f089d57c08f41003ae79458be5011303d81a394dc', 'usage': {'prompt_tokens': 9, 'completion_tokens': 174, 'total_tokens': 183}}\n", "Response from replicate/dolly-v2-12b:ef0e1aefc61f8e096ebe4db6b2bacc297daf2ef6899f0f7e001ec445893500e5 \n", "]\n", "\n", "{'choices': [{'finish_reason': 'stop', 'index': 0, 'message': {'role': 'assistant', 'content': 'Its 68 degrees right now in San Francisco! The temperature will be rising through the week and i expect it to reach 70 on Thursdays and Friday. Skies are expected to be partly cloudy with some sun breaks throughout the day.\\n\\n'}}], 'created': 1691611752.2002115, 'model': 'replicate/dolly-v2-12b:ef0e1aefc61f8e096ebe4db6b2bacc297daf2ef6899f0f7e001ec445893500e5', 'usage': {'prompt_tokens': 9, 'completion_tokens': 48, 'total_tokens': 57}}\n", "Response from replicate/vicuna-13b:6282abe6a492de4145d7bb601023762212f9ddbbe78278bd6771c8b3b2f2a13b \n", "]\n", "\n", "{'choices': [{'finish_reason': 'stop', 'index': 0, 'message': {'role': 'assistant', 'content': ''}}], 'created': 1691611752.8998356, 'model': 'replicate/vicuna-13b:6282abe6a492de4145d7bb601023762212f9ddbbe78278bd6771c8b3b2f2a13b', 'usage': {'prompt_tokens': 9, 'completion_tokens': 0, 'total_tokens': 9}}\n"]}], "source": ["llama_2 = \"replicate/llama-2-70b-chat:2c1608e18606fad2812020dc541930f2d0495ce32eee50074220b87300bc16e1\"\n", "llama_2_7b = \"a16z-infra/llama-2-7b-chat:4f0b260b6a13eb53a6b1891f089d57c08f41003ae79458be5011303d81a394dc\"\n", "dolly_v2 = \"replicate/dolly-v2-12b:ef0e1aefc61f8e096ebe4db6b2bacc297daf2ef6899f0f7e001ec445893500e5\"\n", "vicuna = \"replicate/vicuna-13b:6282abe6a492de4145d7bb601023762212f9ddbbe78278bd6771c8b3b2f2a13b\"\n", "models = [llama_2, llama_2_7b, dolly_v2, vicuna]\n", "for model in models:\n", "  response = completion(model=model, messages=messages)\n", "  print(f\"Response from {model} \\n]\\n\")\n", "  print(response)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zlTVLB-7PTV_", "outputId": "5182275b-3108-46fa-a2cf-745fac4ad110"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hi\n", " there!\n", " The\n", " current\n", " forecast\n", " for\n", " today's\n", " high\n", " temperature\n", " ranges\n", " from\n", " 75\n", " degrees\n", " Fahrenheit\n", " all\n", " day\n", " to\n", " 83\n", " degrees\n", " Fahrenheit\n", " with\n", " possible\n", " isolated\n", " thunderstorms\n", " during\n", " the\n", " afternoon\n", " hours,\n", " mainly\n", " at\n", " sunset\n", " through\n", " early\n", " evening.  The\n", " Pacific\n", " Ocean\n", " has\n", " a\n", " low\n", " pressure\n", " of\n", " 926\n", " mb\n", " and\n", " mostly\n", " cloud\n", " cover\n", " in\n", " this\n", " region\n", " on\n", " sunny\n", " days\n", " due\n", " to\n", " warming\n", " temperatures\n", " above\n", " average\n", " along\n", " most\n", " coastal\n", " areas\n", " and\n", " ocean\n", " breezes.<|USER|>\n"]}], "source": ["# @title Stream Responses from Replicate - Outputs in the same format used by chatGPT streaming\n", "response = completion(model=llama_2, messages=messages, stream=True)\n", "\n", "for chunk in response:\n", "  print(chunk['choices'][0]['delta'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "t7WMRuL-8NrO"}, "outputs": [], "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}