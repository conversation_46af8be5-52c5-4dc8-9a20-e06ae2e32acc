{"cells": [{"cell_type": "markdown", "metadata": {"id": "WemkFEdDAnJL"}, "source": ["## liteLLM Together AI Tutorial\n", "https://together.ai/\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pc6IO4V99O25", "outputId": "2d69da44-010b-41c2-b38b-5b478576bb8b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting litellm\n", "  Downloading litellm-0.1.482-py3-none-any.whl (69 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m69.3/69.3 kB\u001b[0m \u001b[31m757.5 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: importlib-metadata<7.0.0,>=6.8.0 in /usr/local/lib/python3.10/dist-packages (from litellm) (6.8.0)\n", "Collecting openai<0.28.0,>=0.27.8 (from litellm)\n", "  Downloading openai-0.27.9-py3-none-any.whl (75 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.5/75.5 kB\u001b[0m \u001b[31m3.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting python-dotenv<2.0.0,>=1.0.0 (from litellm)\n", "  Downloading python_dotenv-1.0.0-py3-none-any.whl (19 kB)\n", "Collecting tiktoken<0.5.0,>=0.4.0 (from litellm)\n", "  Downloading tiktoken-0.4.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.7 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.7/1.7 MB\u001b[0m \u001b[31m17.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: zipp>=0.5 in /usr/local/lib/python3.10/dist-packages (from importlib-metadata<7.0.0,>=6.8.0->litellm) (3.16.2)\n", "Requirement already satisfied: requests>=2.20 in /usr/local/lib/python3.10/dist-packages (from openai<0.28.0,>=0.27.8->litellm) (2.31.0)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from openai<0.28.0,>=0.27.8->litellm) (4.66.1)\n", "Requirement already satisfied: aiohttp in /usr/local/lib/python3.10/dist-packages (from openai<0.28.0,>=0.27.8->litellm) (3.8.5)\n", "Requirement already satisfied: regex>=2022.1.18 in /usr/local/lib/python3.10/dist-packages (from tiktoken<0.5.0,>=0.4.0->litellm) (2023.6.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai<0.28.0,>=0.27.8->litellm) (3.2.0)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai<0.28.0,>=0.27.8->litellm) (3.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai<0.28.0,>=0.27.8->litellm) (2.0.4)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai<0.28.0,>=0.27.8->litellm) (2023.7.22)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai<0.28.0,>=0.27.8->litellm) (23.1.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai<0.28.0,>=0.27.8->litellm) (6.0.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0.0a3 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai<0.28.0,>=0.27.8->litellm) (4.0.3)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai<0.28.0,>=0.27.8->litellm) (1.9.2)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai<0.28.0,>=0.27.8->litellm) (1.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai<0.28.0,>=0.27.8->litellm) (1.3.1)\n", "Installing collected packages: python-dotenv, tiktoken, openai, litellm\n", "Successfully installed litellm-0.1.482 openai-0.27.9 python-dotenv-1.0.0 tiktoken-0.4.0\n"]}], "source": ["!pip install litellm"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "TMI3739_9q97"}, "outputs": [], "source": ["import os\n", "from litellm import completion\n", "os.environ[\"TOGETHERAI_API_KEY\"] = \"\" #@param\n", "user_message = \"Hello, whats the weather in San Francisco??\"\n", "messages = [{ \"content\": user_message,\"role\": \"user\"}]"]}, {"cell_type": "markdown", "metadata": {"id": "bEqJ2HHjBJqq"}, "source": ["## Calling togethercomputer/llama-2-70b-chat\n", "https://api.together.xyz/playground/chat?model=togethercomputer%2Fllama-2-70b-chat"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Jrrt8puj523f", "outputId": "24494dea-816f-47a6-ade4-1b04f2e9085b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  'choices': [\n", "{\n", "  'finish_reason': 'stop',\n", "  'index': 0,\n", "  'message': {\n", "  'role': 'assistant',\n", "  'content': \"\n", "\n", "I'm not able to provide real-time weather information. However, I can suggest some ways for you to find out the current weather in San Francisco.\n", "\n", "1. Check online weather websites: There are many websites that provide up-to-date weather information, such as AccuWeather, Weather.com, or the National Weather Service. You can enter \"San Francisco\" in the search bar and get the current weather conditions, forecast, and radar imagery.\n", "2. Use a weather app: You can download a weather app on your smartphone that provides real-time weather information. Some popular weather apps include Dark Sky, Weather Underground, and The Weather Channel.\n", "3. <PERSON>ne into local news: You can watch local news channels or listen to local radio stations to get the latest weather forecast and current conditions.\n", "4. Check social media: Follow local weather accounts on social media platforms like Twitter or Facebook to\"\n", "}\n", "}\n", "  ],\n", "  'created': **********.8261144,\n", "  'model': 'togethercomputer/llama-2-70b-chat',\n", "  'usage': {'prompt_tokens': 9, 'completion_tokens': 176, 'total_tokens': 185}\n", "}\n"]}], "source": ["model_name = \"togethercomputer/llama-2-70b-chat\"\n", "response = completion(model=model_name, messages=messages, max_tokens=200)\n", "print(response)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "GIUevHlMvPb8", "outputId": "ad930a12-16e3-4400-fff4-38151e4f6da5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[92mHere's your LiteLLM Dashboard 👉 \u001b[94m\u001b[4mhttps://admin.litellm.ai/6c0f0403-becb-44af-9724-7201c7d381d0\u001b[0m\n", "{\n", "  \"choices\": [\n", "    {\n", "      \"finish_reason\": \"stop\",\n", "      \"index\": 0,\n", "      \"message\": {\n", "        \"content\": \"\\nI'm in San Francisco, and I'm not sure what the weather is like.\\nI'm in San Francisco, and I'm not sure what the weather is like. I'm in San Francisco, and I'm not sure what the weather is like. I'm in San Francisco, and I'm not sure what the weather is like. I'm in San Francisco, and I'm not sure what the weather is like. I'm in San Francisco, and I'm not sure what the weather is like. I'm in San Francisco, and I'm not sure what the weather is like. I'm in San Francisco, and I'm not sure what the weather is like. I'm in San Francisco, and I'm not sure what the weather is like. I'm in San Francisco, and I'm not sure what the weather is like. I'm in San Francisco, and\",\n", "        \"role\": \"assistant\"\n", "      }\n", "    }\n", "  ],\n", "  \"created\": 1692934243.8663018,\n", "  \"model\": \"togethercomputer/CodeLlama-34b-Instruct\",\n", "  \"usage\": {\n", "    \"prompt_tokens\": 9,\n", "    \"completion_tokens\": 178,\n", "    \"total_tokens\": 187\n", "  }\n", "}\n"]}], "source": ["model_name = \"togethercomputer/CodeLlama-34b-Instruct\"\n", "response = completion(model=model_name, messages=messages, max_tokens=200)\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {"id": "sfWtgf-mBQcM"}, "source": ["## With Streaming"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"background_save": true, "base_uri": "https://localhost:8080/"}, "id": "wuBhlZtC6MH5", "outputId": "8f4a408c-25eb-4434-cdd4-7b4ae4f6d3aa"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<async_generator object together_ai_completion_streaming at 0x7d39eeae81c0>\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '\\n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '\\n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'Y'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Com'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'bin'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ator'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' ('}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'Y'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'C'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ')'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' l'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ite'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'LL'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'M'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' are'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' two'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' popular'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' startup'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' acceler'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ators'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' that'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' have'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' gained'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' recognition'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' for'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' their'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' effect'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'iveness'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' in'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'urt'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'uring'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' scaling'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' early'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '-'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'stage'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' companies'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ities'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' they'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' also'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' have'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' distinct'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' differences'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' that'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' set'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' them'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' apart'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' In'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' this'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' ess'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ay'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' we'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' will'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' explore'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' the'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' key'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' features'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' of'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Y'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'C'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' l'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ite'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'LL'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'M'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' discuss'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' which'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' program'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' might'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' be'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' better'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' fit'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' for'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' your'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' startup'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '\\n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '\\n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'Y'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Com'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'bin'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ator'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' is'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' one'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' of'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' the'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' most'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' successful'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' startup'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' acceler'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ators'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' in'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' the'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' world'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' with'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' port'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'folio'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' that'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' includes'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Air'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'b'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'nb'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Drop'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'box'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Red'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'dit'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' F'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ounded'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' in'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' '}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '2'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '0'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '0'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '5'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Y'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'C'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' has'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' fund'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ed'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' over'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' '}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '1'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '9'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '0'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '0'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' start'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ups'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' with'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' combined'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' valu'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ation'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' of'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' over'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' $'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '1'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '0'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '0'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' billion'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' The'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' program'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' is'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' known'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' for'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' its'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' inten'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'se'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' three'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '-'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'month'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' boot'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' camp'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '-'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'style'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' format'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' where'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' found'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ers'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' work'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' closely'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' with'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' experienced'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' ment'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ors'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' to'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' develop'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' their'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' products'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' ref'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ine'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' their'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' business'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' models'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' prepare'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' for'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' fund'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ra'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ising'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Y'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'C'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': \"'\"}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 's'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' focus'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' is'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' on'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' software'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' technology'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' internet'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' start'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ups'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' the'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' program'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' has'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' strong'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' track'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' record'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' of'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' ident'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ifying'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'urt'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'uring'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' successful'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' companies'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' these'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' spaces'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '\\n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '\\n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'l'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ite'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'LL'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'M'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' on'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' the'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' other'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' hand'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' is'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' relatively'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' new'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' acceler'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ator'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' program'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' that'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' was'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' founded'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' in'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' '}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '2'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '0'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '1'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '7'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' While'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' it'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' may'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' not'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' have'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' the'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' same'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' level'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' of'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' brand'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' recognition'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' as'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Y'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'C'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' l'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ite'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'LL'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'M'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' has'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' quickly'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' gained'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' reputation'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' for'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' its'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' unique'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' approach'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' to'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' startup'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' acceleration'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' The'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' program'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' focus'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'es'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' on'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' supporting'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' under'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 're'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'present'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ed'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' found'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ers'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' particularly'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' women'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' people'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' of'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' color'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' provides'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' range'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' of'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' resources'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' support'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' to'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' help'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' these'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' found'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ers'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' succeed'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' l'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ite'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'LL'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'M'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': \"'\"}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 's'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' program'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' is'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' designed'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' to'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' be'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' more'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' flexible'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' personal'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ized'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' than'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' traditional'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' acceler'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ators'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' with'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' focus'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' on'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' connecting'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' found'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ers'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' with'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' ment'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ors'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' resources'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' that'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' are'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' tail'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ored'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' to'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' their'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' specific'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' needs'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '\\n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '\\n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'One'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' key'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' difference'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' between'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Y'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'C'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' l'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ite'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'LL'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'M'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' is'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' the'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' type'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' of'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' companies'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' they'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' support'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Y'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'C'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' focus'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'es'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' primarily'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' on'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' software'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' technology'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' internet'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' start'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ups'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' while'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' l'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ite'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'LL'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'M'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' has'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' bro'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ader'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' focus'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' that'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' includes'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' range'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' of'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' indust'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ries'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' such'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' as'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' health'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'care'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' fin'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ance'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' consumer'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' products'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' This'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' means'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' that'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' if'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' your'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' startup'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' is'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' in'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' non'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '-'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'tech'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' industry'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' l'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ite'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'LL'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'M'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' may'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' be'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' better'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' fit'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '\\n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '\\n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'An'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'other'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' difference'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' between'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' the'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' two'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' programs'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' is'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' their'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' approach'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' to'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' fund'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ing'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Y'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'C'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' provides'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' seed'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' fund'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ing'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' to'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' all'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' of'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' its'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' port'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'folio'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' companies'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' typically'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' in'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' the'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' range'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' of'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' $'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '1'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '0'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '0'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '0'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '0'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' to'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' $'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '2'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '0'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '0'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '0'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '0'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' In'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' contrast'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' l'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ite'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'LL'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'M'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' does'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' not'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' provide'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' fund'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ing'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' to'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' its'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' port'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'folio'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' companies'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' but'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' instead'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' focus'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'es'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' on'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' connecting'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' found'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ers'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' with'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' invest'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ors'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' resources'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' that'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' can'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' help'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' them'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' raise'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' capital'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' This'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' means'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' that'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' if'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' your'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' startup'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' is'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' looking'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' for'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' fund'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ing'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Y'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'C'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' may'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' be'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' better'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' option'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '\\n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '\\n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'So'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' which'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' program'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' is'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' right'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' for'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' your'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' startup'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '?'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' It'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' ultimately'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' depends'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' on'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' your'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' specific'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' needs'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' goals'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' If'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' your'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' startup'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' is'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' in'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' non'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '-'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'tech'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' industry'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' l'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ite'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'LL'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'M'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': \"'\"}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 's'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' bro'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ader'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' focus'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' may'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' be'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' better'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' fit'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Additionally'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' if'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' you'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': \"'\"}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 're'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' looking'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' for'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' more'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' personal'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ized'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' flexible'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' approach'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' to'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' acceleration'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' l'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ite'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'LL'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'M'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': \"'\"}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 's'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' program'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' may'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' be'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' better'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' choice'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' On'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' the'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' other'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' hand'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' if'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' your'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' startup'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' is'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' in'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' the'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' software'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' technology'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' or'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' internet'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' space'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' you'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': \"'\"}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 're'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' looking'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' for'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' seed'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' fund'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ing'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Y'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'C'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': \"'\"}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 's'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' program'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' may'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' be'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' a'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' better'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' fit'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '\\n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '\\n'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'In'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' conclusion'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' Y'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'C'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' l'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ite'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'LL'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'M'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' are'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' both'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' excellent'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' startup'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' acceler'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ators'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' that'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' can'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' provide'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' valuable'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' resources'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' support'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' to'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' early'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '-'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'stage'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' companies'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' While'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' they'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' share'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' some'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' similar'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 'ities'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' they'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' also'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' have'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' distinct'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' differences'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' that'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' set'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' them'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' apart'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' By'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' considering'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' your'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' startup'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': \"'\"}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': 's'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' specific'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' needs'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' and'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' goals'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ','}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' you'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' can'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' determine'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' which'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' program'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' is'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' the'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' best'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' fit'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' for'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' your'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': ' business'}}]}\n", "{'choices': [{'delta': {'role': 'assistant', 'content': '.'}}]}\n"]}], "source": ["user_message = \"Write 1page essay on YC + liteLLM\"\n", "messages = [{ \"content\": user_message,\"role\": \"user\"}]\n", "\n", "\n", "async def parse_stream(stream):\n", "    async for elem in stream:\n", "        print(elem)\n", "    return\n", "\n", "stream = completion(model=\"togethercomputer/llama-2-70b-chat\", messages=messages, stream=True, max_tokens=800)\n", "print(stream)\n", "\n", "# Await the asynchronous function directly in the notebook cell\n", "await parse_stream(stream)\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}